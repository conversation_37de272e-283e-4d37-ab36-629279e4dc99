import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/polling_services/polliing_services.dart';
import 'package:wd/features/page/0_tiktok/video_home_cubit.dart';
import 'package:wd/features/page/1_game_home/game_home_cubit.dart';
import 'package:wd/features/page/3_chat/chat_cubit.dart';
import 'package:get_it/get_it.dart';

import 'features/page/0_tiktok/video_library/popular_video/popular_video_filter/popular_video_filter_cubit.dart';
import 'features/page/0_tiktok/video_library/video_library_cubit.dart';
import 'features/page/2_activity/activity_list_cubit.dart';
import 'features/page/3_transact/withdraw/withdraw_cubit.dart';
import 'features/page/4_mine/mine_v2_cubit.dart';
import 'features/page/4_mine/notifications/notification_cubit.dart';
import 'features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart';
import 'features/page/main/screens/main_screen_cubit.dart';
import 'features/page/3_transact/topup/topup_cubit.dart';
import 'features/page/3_transact/transact_cubit.dart';
import 'features/routers/navigator_utils.dart';

final sl = GetIt.instance;

Future<void> init() async {
  sl.registerSingleton<UserCubit>(UserCubit());
  sl.registerSingleton<NavigatorService>(NavigatorService());
  sl.registerSingleton<MainScreenCubit>(MainScreenCubit());
  sl.registerSingleton<GameHomeCubit>(GameHomeCubit());
  sl.registerLazySingleton<VideoHomeCubit>(()=>VideoHomeCubit());
  sl.registerLazySingleton<VideoLibraryCubit>(()=>VideoLibraryCubit());
  sl.registerLazySingleton<ActivityListCubit>(() => ActivityListCubit());
  sl.registerSingleton<MineV2Cubit>(MineV2Cubit());
  sl.registerSingleton<ChatCubit>(ChatCubit());
  sl.registerSingleton<NotificationsCubit>(NotificationsCubit());
  sl.registerSingleton<PromotionRewardsCubit>(PromotionRewardsCubit());
  sl.registerSingleton<PopularVideoFilterCubit>(PopularVideoFilterCubit());
  sl.registerLazySingleton<PollingService>(() => PollingService());
}
