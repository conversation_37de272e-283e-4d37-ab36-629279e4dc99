import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/bank_entity.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/models/entities/image_captcha_entity.dart';
import 'package:wd/core/models/entities/login_entity.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/models/entities/tc_sdk_config_entity.dart';
import 'package:wd/core/models/entities/country_entity.dart';
import 'package:wd/core/models/entities/user_info_entity.dart';
import 'package:wd/core/models/entities/user_vip_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/http/https.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/injection_container.dart';

class UserApi {
  /// 获取系统配置
  static Future<SystemConfigEntity?> fetchSystemConfig() async {
    ResponseModel? res = await Http().request<SystemConfigEntity>(
      ApiConstants.systemConfig,
      needShowToast: false,
      needSignIn: false,
    );
    return res.isSuccess ? res.data : null;
  }

  /// 获取区号列表
  static Future<CountryEntity?> fetchAreaCodes() async {
    ResponseModel? res = await Http().request<CountryEntity>(
      ApiConstants.getAreaCode,
      method: HttpMethod.get,
      needShowToast: false,
      needSignIn: false,
    );

    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return null;
    }
  }

  /// 用户注册
  static Future<({LoginTokenUser? tokenUser, String? errorStr})> doRegisterAndLogin({
    required String userName,
    required String password,
    required String invitationCode,
    required String channelCode,
    bool? isPhoneNo,
    String? verificationCode,
    String? verificationUUID,
    String? wangYiCaptcha,
    String? smsCode,
    int? areaCode,
  }) async {
    ResponseModel? res = await Http().request<LoginEntity>(
      ApiConstants.registerAndLogin,
      params: {
        'userNo': userName,
        'loginPwd': password,
        'confirmLoginPwd': password,
        'invitationCode': invitationCode,
        'channelCode': channelCode,
        if (sl<UserCubit>().state.remotePushDeviceToken != null) 'jgToken': sl<UserCubit>().state.remotePushDeviceToken,
        if (verificationCode != null) 'code': verificationCode,
        if (verificationUUID != null) 'uuid': verificationUUID,
        if (wangYiCaptcha != null) 'validate': wangYiCaptcha,
        if (isPhoneNo != null) 'isPhoneNo': isPhoneNo,
        if (smsCode != null) 'smsCode': smsCode,
        if (areaCode != null) 'areaCode': areaCode,
        'loginClient': 2, // iOS client
      },
      needSignIn: false,
      needEncrypt: true,
    );
    if (res.isSuccess && res.data != null && res.data.tokenUser != null) {
      LoginTokenUser model = res.data.tokenUser;
      return (tokenUser: model, errorStr: null);
    }
    return (tokenUser: null, errorStr: res.msg);
  }

  /// 发送短信验证码
  static Future<bool> doSendSmsCode({
    required String phoneNo,
    int? areaCode,
  }) async {
    ResponseModel? res = await Http().request<String>(
      method: HttpMethod.get,
      ApiConstants.sendSmsCode,
      params: {
        'phone': phoneNo,
        if (areaCode != null) 'areaCode': areaCode,
      },
      needSignIn: false,
    );
    if (res.isSuccess && res.data != null) {
      return true;
    }
    return false;
  }

  /// 发送邮箱验证码
  static Future<bool> doSendMailCode({
    required String mail,
  }) async {
    ResponseModel? res = await Http().request<String>(
      method: HttpMethod.post,
      ApiConstants.sendMailCode,
      params: {
        'mail': mail,
      },
      needSignIn: false,
    );
    if (res.isSuccess && res.data != null) {
      return true;
    }
    return false;
  }

  /// 重置密码
  static Future<bool> doRecoverPassword({
    required String phoneNo,
    required String smsCode,
    required String pwd,
    required String confirmPwd,
  }) async {
    ResponseModel? res = await Http().request<String>(
      ApiConstants.recoverPassword,
      params: {
        'phoneNo': phoneNo,
        'smsCode': smsCode,
        'confirmPwd': confirmPwd,
        'pwd': pwd,
      },
      needSignIn: false,
      needEncrypt: true,
    );
    return res.isSuccess;
  }

  /// 用户登录
  static Future<LoginTokenUser?> doLogin(
    LoginType loginType, {
    String? username,
    String? password,
    String? verificationCode,
    String? verificationUUID,
    String? thirdToken,
    String? wangYiCaptcha,
    String? smsCode,
    int? areaCode,
  }) async {
    LogD("sl<UserCubit>().state.remotePushDeviceToken>>>> ${sl<UserCubit>().state.remotePushDeviceToken}");
    ResponseModel? res = await Http().request<LoginEntity>(
      ApiConstants.login,
      params: {
        'authMethod': loginType.code,
        if (username != null) 'userNo': username,
        if (thirdToken != null) 'thirdToken': thirdToken,
        if (password != null) 'loginPwd': password,
        if (sl<UserCubit>().state.remotePushDeviceToken != null) 'jgToken': sl<UserCubit>().state.remotePushDeviceToken,
        if (verificationCode != null) 'code': verificationCode,
        if (verificationUUID != null) 'uuid': verificationUUID,
        if (wangYiCaptcha != null) 'validate': wangYiCaptcha,
        if (smsCode != null) 'smsCode': smsCode,
        if (areaCode != null) 'areaCode': areaCode,
        'loginClient': 2, // iOS client
        'isPhoneNo': loginType == LoginType.phone,
      },
      needSignIn: false,
    );

    if (res.isSuccess && res.data != null && res.data.tokenUser != null) {
      LoginTokenUser model = res.data.tokenUser;
      return model;
    }
    return null;
  }

  /// 用户退出登录
  static Future<bool> doLogout() async {
    ResponseModel? res = await Http().request<dynamic>(
      ApiConstants.logout,
      needShowToast: false,
    );
    return res.isSuccess;
  }

  /// 获取图形验证码
  static Future<ImageCaptchaEntity?> fetchCaptcha() async {
    ResponseModel? res = await Http().request<ImageCaptchaEntity>(ApiConstants.getCaptcha, needSignIn: false);
    if (res.isSuccess) {
      ImageCaptchaEntity model = res.data;
      return model;
    }
    return null;
  }

  /// 获取用户信息
  static Future<UserInfoEntity?> fetchUserInfo() async {
    ResponseModel? res = await Http().request<UserInfoEntity>(ApiConstants.userInfo);
    if (res.isSuccess && res.data != null) {
      UserInfoEntity model = res.data;
      return model;
    }
    return null;
  }

  /// 获取用户余额
  static Future<UserBalanceEntity?> fetchBalanceInfo() async {
    ResponseModel? res = await Http().request<UserBalanceEntity>(
      ApiConstants.userBalanceRefresh,
    );
    if (res.isSuccess && res.data != null) {
      UserBalanceEntity model = res.data;
      return model;
    }
    return null;
  }

  /// 获取用户腾讯im的UserSig
  static Future<TCSDKConfigEntity?> fetchTencentUserSig() async {
    final res = await Http().request<TCSDKConfigEntity>(ApiConstants.tcUserSig);
    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  /// 修改头像
  static Future<bool> updateAvatar(int faceId) async {
    ResponseModel res = await Http().request(
      ApiConstants.updateAvatar,
      params: {
        'faceId': faceId,
      },
      isFormUrlEncoded: true,
    );
    if (res.isSuccess) {
      return true;
    } else {
      return false;
    }
  }

  /// 修改生日
  static Future<bool> updateBirthday(DateTime birthday) async {
    ResponseModel res = await Http().request(
      ApiConstants.updateBirthday,
      params: {
        'birthday': birthday.toIso8601String(),
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 修改性别
  static Future<bool> updateGender(int gender) async {
    ResponseModel res = await Http().request(
      ApiConstants.updateGender,
      params: {
        'gender': gender,
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 设置真实姓名 （只能设置一次）
  static Future<bool> setRealName(String realName) async {
    ResponseModel res = await Http().request(
      ApiConstants.updateRealName,
      params: {'depositor': realName},
    );
    return res.isSuccess;
  }

  /// 修改昵称
  static Future<bool> updateNickName(String nickName) async {
    ResponseModel res = await Http().request(
      ApiConstants.updateNickName,
      params: {
        'nickName': nickName,
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 修改手机
  static Future<bool> updatePhone(String phoneNo, String fundPwd) async {
    ResponseModel res = await Http().request(
      ApiConstants.updatePhone,
      params: {
        'phoneNo': phoneNo,
        'fundPwd': fundPwd,
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 修改邮箱
  static Future<bool> updateEmail(String email, String fundPwd) async {
    ResponseModel res = await Http().request(
      ApiConstants.updateEmail,
      params: {
        'email': email,
        'fundPwd': fundPwd,
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 修改是否显示视频
  static Future<bool> updateMovieTabVisible(bool isVisible) async {
    ResponseModel res = await Http().request(
      ApiConstants.updateMovieView,
      params: {
        'movieView': isVisible,
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  // 修改密码，SetPasswordType：修改登录密码、首次设置资金密码、修改资金密码
  static Future<bool> updatePassword(SetPasswordType type, String pwdA, String pwdB) async {
    String url = ApiConstants.updateLoginPwd;
    Map<String, dynamic> params = {};
    switch (type) {
      case SetPasswordType.modifyLoginPwd:
        url = ApiConstants.updateLoginPwd;
        params.addAll({'oldLoginPwd': pwdA, 'loginPwd': pwdB});
        break;
      case SetPasswordType.setFundPwd:
        url = ApiConstants.setFundPwd;
        params.addAll({'loginPwd': pwdA, 'fundPwd': pwdB});
        break;
      case SetPasswordType.modifyFundPwd:
        url = ApiConstants.updateFunPwd;
        params.addAll({'oldFundPwd': pwdA, 'fundPwd': pwdB});
        break;
    }

    ResponseModel res = await Http().request(
      url,
      params: params,
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 获取用户绑定银行卡列表, type 1:银行卡  2:钱包
  static Future<List<PaymentCardEntity>> fetchBindBankCardList({required int type, int? wldType}) async {
    ResponseModel? res = await Http().request<PaymentCardEntityList>(
      ApiConstants.bindBankList,
      params: {
        'type': type,
        'wldType': wldType,
      },
      isFormUrlEncoded: true,
    );
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    }
    return [];
  }

  /// 获取银行列表, type 1:银行卡  2:钱包
  static Future<List<BankEntity>> fetchBankList(WithdrawType type) async {
    ResponseModel? res = await Http().request<BankEntityList>(
      ApiConstants.bankList,
      params: {
        'type': type.index + 1,
      },
      isFormUrlEncoded: true,
    );
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    }
    return [];
  }

  /// 用户绑定银行卡, type 1:银行卡  2:钱包
  static Future<bool> addBankCard({
    required WithdrawType type,
    required String bankCode,
    required String bankName,
    required String cardNo,
    String? bankAddress,
    required String realName,
    required String fundPwd,
    int? wldType,
  }) async {
    ResponseModel? res = await Http().request(ApiConstants.userAddBankCard, params: {
      "type": type.index + 1,
      "userNo": sl<UserCubit>().state.userInfo?.userNo ?? '',
      "bankCode": bankCode,
      "bankName": bankName,
      if (bankAddress != null) "branchBankAddress": bankAddress,
      "realName": realName,
      "cardNo": cardNo,
      "fundPwd": fundPwd,
      if (wldType != null) "wldType": wldType,
    });
    final flag = res.isSuccess;
    if (flag) {
      sl<UserCubit>().resetFundPasswordErrorLock();
    }
    return flag;
  }

  /// 获取用户VIP信息
  static Future<UserVipEntity?> fetchVipInfo() async {
    ResponseModel? res = await Http().request<UserVipEntity>(ApiConstants.userVipInfo);
    if (res.isSuccess) {
      return res.data;
    } else {
      return null;
    }
  }

  /// 获取用户签到数据列表
  static Future<DailyCheckInEntity?> fetchCheckInList() async {
    // ResponseModel? res = await Http().request<DailySignInList>(ApiConstants.userCheckInInfo, needSignIn: false);
    ResponseModel? res = await Http().request<DailyCheckInEntity>(
      ApiConstants.userCheckInInfoV2,
      method: HttpMethod.get,
      needSignIn: false,
    );
    if (res.isSuccess) {
      return res.data;
    } else {
      return null;
    }
  }

  /// 清空用户签到数据
  static Future<bool> clearUserCheckInInfo() async {
    ResponseModel? res = await Http().request<DailyCheckInEntity>(
      ApiConstants.clearUserCheckInInfo,
      method: HttpMethod.get,
      needSignIn: false,
    );
    return res.isSuccess;
  }

  /// 用户签到 isBackdate:是否补签
  static Future<(bool, String?)> executeCheckIn({required bool isBackdate, required int date}) async {
    ResponseModel? res = await Http().request(
        isBackdate ? ApiConstants.userBackdateCheckIn : ApiConstants.userCheckInV2,
        params: {if (isBackdate) "reSignInDay": date, if (!isBackdate) "signInDay": date},
        needShowToast: false);
    return (res.isSuccess, res.msg);
  }

  /// 检查手机号是否在群组中
  static Future<bool> checkPhoneIsGroup(String phoneNo, String activeId) async {
    ResponseModel? res = await Http().request(
      ApiConstants.checkPhoneIsGroup,
      method: HttpMethod.get,
      params: {'phoneNo': phoneNo, 'activeId': activeId},
    );

    return res.isSuccess;
  }

  /// 更新手机号
  static Future<bool> updatePhoneNo(String oldPhoneSmsCode, String newPhoneNo, String newPhoneSmsCode) async {
    ResponseModel? res = await Http().request(ApiConstants.updatePhoneNo, params: {
      'oldPhoneSmsCode': oldPhoneSmsCode,
      'newPhoneNo': newPhoneNo,
      'newPhoneSmsCode': newPhoneSmsCode,
    });
    return res.isSuccess;
  }

  /// 绑定手机号
  static Future<bool> bindPhoneNo(String phoneNo, String code) async {
    ResponseModel? res =
        await Http().request(ApiConstants.bindPhoneNo, params: {'phoneNo': phoneNo, 'phoneSmsCode': code});
    return res.isSuccess;
  }

  /// 检查手机号是否绑定用户
  static Future<bool> checkPhoneIsBindUser(String phoneNo) async {
    ResponseModel? res = await Http().request(
      ApiConstants.checkPhoneIsBindUser,
      method: HttpMethod.get,
      needSignIn: false,
      params: {'phone': phoneNo},
    );

    if (res.isSuccess) {
      return res.data;
    }
    return false;
  }

  /// 测试环境自动获取短信验证码
  static Future<String> testUseGetSmsCode(String phoneNo) async {
    ResponseModel? res = await Http().request(
      ApiConstants.testUseGetSmsCode,
      method: HttpMethod.get,
      needSignIn: false,
      needShowToast: false,
      params: {'phone': phoneNo},
    );
    return res.isSuccess ? res.data : '';
  }

  /// 更新用户语言
  static Future<bool> updateLanguage(String languageTag) async {
    ResponseModel? res = await Http().request(
      ApiConstants.updateLanguage + languageTag,
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.isSuccess;
  }
}
