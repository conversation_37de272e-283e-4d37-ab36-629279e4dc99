import 'package:flutter/foundation.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/core/utils/global_config.dart';

import 'constants.dart';

class ApiConstants {
  /// 获取当前应用使用的基础 URL
  ///
  /// 在 Web 平台的 **正式环境** 下，返回当前浏览器地址的 origin，
  /// 例如：如果浏览器地址是 https://xxx.com/path?id=123
  /// 则返回：https://xxx.com
  ///
  /// 在其它平台（如 Android/iOS）或调试模式下，返回配置文件中的 appBaseUrl。
  static get url {
    // 判断是否运行在 Web 平台，并且是「非调试模式」

    if (SystemUtil.isWeb() && !kDebug && !kDebugMode) {
      // 返回浏览器当前地址的 origin（协议 + 主机 + 端口）
      // 示例输出: "https://example.com" 或 "http://localhost:8080"
      return Uri.base.origin;
    }

    // 如果是非 Web 或者是调试环境，则返回全局配置的 Base URL
    // 通常用于开发环境或 App 平台的 API 地址
    return GlobalConfig().appBaseUrl;
  }

  static const String apiVersion = '/api';

  static String baseUrl = 'https://wdclus001.com';
  // static String baseUrl = '$url';

  static const String list = "$apiVersion/list"; // 全部游戏列表
  // static const String gameList = "$list/game"; // 全部游戏列表
  static const String gameListWithPlatformInfo = "$list/getGameListInfo"; // 全部游戏列表+平台图标
  static const String bankList = '$list/bank'; // 获取支持的银行列表
  static const String vipList = '$list/vip'; // vip 列表
  static const String userStatementFilterWay = '$list/fundChangeWay'; // 账变记录-获取交易方式，模式列表
  static const String userStatementFilterType = '$list/fundChangeType'; // 账变记录-交易类别列表
  static const String userTopUpHistoryFilterWay = '$list/cashinWay'; // 充值记录-入款方式列表
  static const String userTopUpHistoryFilterType = '$list/cashinType'; // 充值记录-入款类型列表
  static const String userWithdrawHistoryFilterWay = '$list/cashoutWay'; // 提现记录-出款方式列表
  static const String userWithdrawHistoryFilterType = '$list/cashoutType'; // 提现记录-出款类型列表
  static const String gameNoticeList = "$apiVersion/server/noticeInfo/list";
  static const String getLastsBonusPool = '$apiVersion/index/getLastsBonusPool'; // 获取最近奖池数据
  static const String homeBannerList = '$apiVersion/index/bannerList'; // 首页-轮播图列表

  // Channel 三方游戏模块
  static const String channel = "$apiVersion/channel";
  static const String gameListV2 = "$channel/queryGame/getGameClass"; // 首页场馆列表
  static const String platformGameList = "$channel/queryGame/gameList"; // 查询某场馆的游戏列表
  static const String gameLogin = "$channel/login"; // 三方游戏接口-三方游戏登录
  static const String gameAmountTransferTo = "$channel/amountTransferInto"; // 三方游戏接口-额度划入三方平台
  static const String gameAmountTransferOut = "$channel/amountTransferOut"; // 三方游戏接口-额度划出三方平台
  static const String gameAmountAllTransferOut = "$channel/allTransferOut"; // 三方游戏接口-所有额度划出三方平台
  static const String gameAmount = "$channel/searchAmount"; // 三方游戏接口-查看第三方余额(刷新)
  static const String gameAmountAll = "$channel/searchAllAmount"; // 三方游戏接口-查看各平台余额
  static const String gameAllTypeRecord = "$channel/searchAllGameTypeResult"; // 三方游戏接口-查看游戏类型总记录
  static const String gameOneTypeRecord = "$channel/searchAllGamePlatFormResult"; // 三方游戏接口-查看某一个游戏类型记录
  static const String gameMainPlatformRecord = "$channel/searchGameRecord"; // 三方游戏接口-查看游戏记录
  static const String gameSubPlatformRecord = "$channel/searchMemberGameRecord"; // 三方游戏接口-查看下级游戏记录
  static const String gameWinners = "$channel/getUserBetRecord"; // 获取用户中奖记录

  // 游戏收藏
  static const String gamSavour = '$apiVersion/savour'; // 收藏
  static const String submitGameFav = '$gamSavour/user/savourGame'; // 收藏游戏
  static const String cancelGameFav = '$gamSavour/user/cancelSavourGame'; // 取消收藏游戏
  static const String popularAndFavGameList = '$gamSavour/user/popularAndUserSavour'; // 首页热门游戏+收藏
  static const String favGameList = '$gamSavour/user/getSavourGame'; // 游戏收藏列表

  // Chat 聊天
  static const String chatType = "$apiVersion/index/chatType"; // 聊天跳转类型

  // Tencent IM 腾讯IM模块
  static const String tc = "$apiVersion/tc";
  static const String tcUserSig = "$tc/userSig";
  static const String tcAllOnlineSupport = "$tc/getAllOnlineSupport"; // 获取客服在线状态列表
  static const String tcCheckServiceOnlineStatus = "$tc/checkSupportOnlineStatus"; // 通知后端检查客服在线状态

  // redEnvelope 红包
  static const String redEnvelope = "$apiVersion/redEnvelope";
  static const String redEnvelopeGet = "$redEnvelope/get";
  static const String redEnvelopeStatus = "$redEnvelope/check";

  // Video 视频模块
  static const String video = "$apiVersion/video";
  static const String videoFilterList = "$video/videoTag"; // 视频列表筛选条件
  static const String videoList = "$video/en/videoLibraryList"; // 视频列表
  // static const String videoList = "$video/videoLibraryList"; // 视频列表
  static const String videoLinkList = "$video/videoLineList"; // 某一视频线路列表
  static const String videoDetail = "$video/en/videoDetails"; // 视频详情
  static const String shortVideoList = "$video/en/shortVideoList"; // 短视频/黑料
  static const String videoOperaLike = "$video/like"; // 视频 点赞
  static const String videoOperaUnlike = "$video/cancelLike"; // 视频 取消点赞
  static const String videoWatched = "$video/en/watched"; // 已观看
  static const String videoHotTags = "$video/getHotMovieTag"; // 热门视频标签
  static const String videoHotMovies = "$video/en/hotMovies"; // 电影热门分类分组前6部电影
  static const String videoHotMoviesList = "$video/en/hotMoviesList"; // 热门视频列表

  // User 用户模块
  static const String user = '$apiVersion/user';
  static const String systemConfig = '$user/api/config';
  static const String getAreaCode = '$user/getAreaCode'; // 获取区号列表
  static const String getCaptcha = '$user/getVerify'; // 验证码
  static const String login = '$user/multiModeLogin'; // 登录
  static const String logout = '$user/logout'; // 退出登录
  // static const String registerAndLogin = '$user/register/en/regAndLogin'; // 注册并登录
  // static const String registerAndLogin = '$user/register/v2/regAndLogin'; // 注册并登录
  static const String registerAndLogin = '$user/register/quickRegister'; // 注册并登录
  static const String userInfo = '$user/center/info'; // 用户信息
  static const String userBalanceRefresh = '$user/center/refreshAmount'; // 用户刷新余额
  static const String updateAvatar = '$user/center/updateFace'; // update avatar
  static const String updateBirthday = '$user/center/updateBirthday'; // update birthday
  static const String updateGender = '$user/center/updateGender'; // update gender
  static const String updateRealName = '$user/center/setRealName'; // update nickName
  static const String updateNickName = '$user/center/updateNickName'; // update nickName
  static const String updateMovieView = '$user/center/updateMovieView'; // 是否隐藏视频
  static const String updatePhone = '$user/center/updatePhoneNo'; // update phone
  static const String updateEmail = '$user/center/updateEmail'; // update email
  static const String setFundPwd = '$user/center/setFundPwd'; // 设置资金密码
  static const String updateFunPwd = '$user/center/updateFundPwd'; // 修改资金密码
  static const String updateLoginPwd = '$user/center/updateLoginPwd'; // 修改登录密码
  static const String bindBankList = '$user/center/userBankInfo'; // 获取绑定银行卡列表
  static const String userAddBankCard = '$user/center/addUserBankInfo'; // 绑定银行卡
  static const String userVipInfo = '$user/center/currentVipLevel'; // 获取用户VIP信息
  static const String userCheckInInfo = '$user/center/getSignInfo'; // 获取用户签到信息
  static const String userCheckIn = '$user/center/doSign'; // 用户签到
  static const String userInviteLink = '$user/inviteCode/getInviteLinkByUserNo'; // 获取用户邀请链接
  static const String updateLanguage = '$user/center/updateLanguage/'; // 设置用户语言

  static const String checkIn = '$apiVersion/signIn';
  static const String userCheckInV2 = '$checkIn/user/signIn'; // 用户签到 v2
  static const String userBackdateCheckIn = '$checkIn/user/reSignIn'; // 用户补签
  static const String userCheckInInfoV2 = '$checkIn/user/loadSignInInfo'; // 用户签到信息
  static const String clearUserCheckInInfo = '$checkIn/user/clearSignIn'; // 清空用户签到记录

  // VideoValid 视频验证模块
  static const String videoValid = '$apiVersion/videoValid';
  static const String submitVideoCoupon = '$videoValid/exchange'; // 提交视频兑换码
  static const String videoVIPRemainDays = '$videoValid/getUserValidDays'; // 视频vip剩余天数

  // Agent
  static const String agent = '$apiVersion/agent';
  static const String inviteCodeInfo = '$agent/getInviteCodeInfo'; // 获取邀请码信息
  static const String subordinateInfo = '$agent/getSubordinateInfo'; // 获取下级信息
  static const String friendCommissionRecords = '$agent/getFriendCommissionRecords'; // 获取下级信息

  // Notifications
  static const String noticeList = '$user/center/siteMessageList'; // all notifications
  static const String updateNoticeRead = '$user/center/readSiteMessage'; // mark read notifications
  static const String notificationAlert = '$apiVersion/index/notice'; // alert notifications
  static const String noticeUnreadCount = '$user/center/siteMessageUnReadCount'; // unread count
  static const String updateAllNoticeRead = '$user/center/readSiteMessage'; // mark all read notifications
  // Report 记录
  static const String report = '$apiVersion/report';
  static const String userStatement = '$report/fundChangeRecord'; // Statement 账变记录
  static const String userTopUpHistoryList = '$report/cashinRecord'; // 充值记录
  static const String userWithdrawHistoryList = '$report/cashoutRecord'; // 提现记录
  static const String operateWithdrawRecordRead = '$apiVersion/index/read'; // 标记提现记录已读

  // Activity 活动
  static const String activity = '$apiVersion/active';
  static const String activityCategoryList = '$activity/category'; // 活动列表分类
  static const String activityList = '$activity/list'; // 游戏活动列表
  static const String taskList = '$activity/receive/list'; // 自助领取活动列表
  static const String activityCollectionStatus = '$activity/userActiveStatus'; // 领取状态
  // static const String activityGetCoin = '$activity/get'; // 领取活动彩金 removed
  static const String activityApply = '$activity/apply'; // 申请活动
  static const String activityTaskRecordList = '$activity/receive/record'; // 活动-自助领取记录
  static const String activityTaskComplete = '$activity/receive/submit'; // 自助领取活动提交>>完成任务

  // Lobby 大厅
  static const String lobbyRedPacket = '$apiVersion/active/lobbyRedPacket';
  static const String lobbyRedPacketCheckEligibility = '$lobbyRedPacket/checkUserEligibility';
  static const String lobbyRedPacketReceive = '$lobbyRedPacket/receive';

  // cashIn 充值中心
  static const String cashIn = '$apiVersion/cashin';
  static const String onlinePaymentList = '$cashIn/userOnlinePayList'; // 充值列表
  static const String submitPayment = '$cashIn/submitOnlinePay'; // 提交充值订单
  static const String submitOfflineOrder = '$cashIn/submitOfflinePay'; // 提交线下充值订单
  static const String thirdPartyWalletBalance =
      '$cashIn/wallet/checkBalance'; // (三方钱包)查询虚拟币钱包余额,没有账户则后台自动创建一个(当前仅支持UPAY_WALLET)
  static const String thirdPartyWalletLobbyLink = '$cashIn/wallet/getLobbyLink'; // (三方钱包)获取三方钱包充值大厅跳转链接
  static const String thirdPartyWalletSubmitCharge = '$cashIn/wallet/submitCharge'; // (三方钱包)提交充值

  // cashOut 提现中心
  static const String cashOut = '$apiVersion/cashout';
  static const String cashOutAvailable = '$cashOut/status'; // 提现-是否可提款
  static const String cashOutUserBankInfo = '$cashOut/userBankInfo'; // 提现-出款用户信息
  static const String cashOutUserBankInfoList = '$cashOut/userBankInfoList'; // 提现-用户银行信息列表
  static const String cashOutManualChannelList = '$cashOut/getOfflineWithdrawalType'; // 提现-人工提现通道列表
  static const String cashOutSubmitByBank = '$cashOut/submitByBank'; // 提现-提交出款请求(银行卡)
  static const String cashOutSubmitByVirtualCurrency = '$cashOut/submitByVirtualCurrency'; // 提现-提交出款请求（虚拟币）

  // lottery 彩票
  static const String lottery = '$apiVersion/lottery';
  static const String winningList = "$lottery/find/winning"; // 首页中奖名单列表
  static const String lotteryList = '$lottery/groupList'; // 彩票-自营彩票列表
  static const String lotteryCurrentState = '$lottery/index'; // 彩票-获取期号
  static const String lotteryTodayResult = '$lottery/todayOpenTimeRecordByPage'; // 彩票-今天的开奖结果
  static const String lotteryOdds = '$lottery/odds'; // 彩票-获取赔率
  static const String lotteryChangLong = '$lottery/changlong'; // 彩票-长龙
  static const String lotterySubmit = '$lottery/bet'; // 彩票-下注
  static const String betRecord = '$lottery/betRecord'; // Lottery-Bet-Records
  static const String matchResult = '$lottery/matchResult';
  static const String lotteryCurrentStateV2 = '$lottery/v2/index'; // 获取当前期号
  static const String lotteryClick = '$lottery/lotteryClick'; // 彩票点击次数统计
  static const String lotteryRule = '$lottery/rule'; // 彩票规则

  //promotion 推广
  static const String promotion = '$apiVersion/promotion';
  static const String team = '$promotion/team'; // 团队信息
  static const String myTeam = '$promotion/my/team'; // 我的团队信息
  static const String commission = '$promotion/commission'; // 佣金信息
  static const String myTeamMembers = '$promotion/my/team/members'; // 我的团队成员信息
  static const String myTeamDetail = '$promotion/my/team/detail'; // 我的团队详细信息
  static const String commissionDetail = '$promotion/commission/detail'; // 佣金详细信息
  static const String promotionCashInConfig = '$promotion/cashinConfig'; // 推广-充值配置
  static const String promotionBetConfig = '$promotion/betConfig'; // 推广-投注配置
  static const String commissionReceive = '$promotion/commission/receive'; // 佣金领取

  // // animalGame 动物游戏
  // static const String bet = '$lottery/bet';
  // appVersion app版本更新
  static const String checkAppVersion = '$apiVersion/appUpdate/getLatestAppVersionInfo';

  // 推送
  static const String uploadRemotePushDeviceToken = "$apiVersion/jPush/deviceSave"; // 保存推送token

  //sms
  static const String sendSmsCode = '$apiVersion/sms/sendSmsCode'; // Send SMS verification code
  static const String recoverPassword = '$apiVersion/sms/recoverPassword'; // Reset password via SMS
  static const String updatePhoneNo = '$apiVersion/sms/updatePhoneNo'; // Update phone number
  static const String bindPhoneNo = '$apiVersion/sms/userBindPhoneNo'; // Bind phone number to user account
  static const String checkPhoneIsBindUser =
      '$apiVersion/sms/checkPhoneIsBindUser'; // Check if phone number is bound to a user
  static const String testUseGetSmsCode = '$apiVersion/sms/testUseGetSmsCode'; // Get test SMS code

  //mail
  static const String sendMailCode = '$apiVersion/mail/userBindMail'; // Send email verification code

  // active 活动
  static const String active = '$apiVersion/active';
  static const String checkPhoneIsGroup = '$active/checkPhoneIsGroup'; // 检查手机号是否在群组中
  static const String customerServiceConfig = '$apiVersion/index/cs/list'; // 客服配置
}
