import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/payment_list_state.dart';
import 'package:wd/features/page/4_mine/profile/widgets/profile_field.dart';
import 'package:wd/features/page/4_mine/profile/widgets/profile_section.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_copy_button.dart';
import 'package:wd/shared/widgets/common_switch.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'account_security_cubit.dart';
import 'account_security_state.dart';

/// 安全中心
class AccountSecurityPage extends BasePage {
  const AccountSecurityPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AccountSecurityPageState();
}

class _AccountSecurityPageState extends BasePageState<AccountSecurityPage> {
  @override
  void initState() {
    pageTitle = "security".tr();
    super.initState();
  }

  void _navigateToUpdateProfileScreen(BuildContext context, String initialValue, UserFieldType field) async {
    final updatedValue = await sl<NavigatorService>().push(
      AppRouter.updateProfile,
      arguments: {
        'initialValue': initialValue,
        'field': field,
      },
    );

    if (updatedValue != null && context.mounted) {
      // Update the AccountSecurityCubit state with the new value
      BlocProvider.of<AccountSecurityCubit>(context).updateField(field, updatedValue);
    }
  }

  buildImageWidget(String imageUrl) =>
      Padding(padding: EdgeInsets.only(right: 8.gw), child: Image.asset(imageUrl, width: 24.gw, height: 24.gw));

  // Basic Information Section
  Widget _buildBasicInformationSection(BuildContext context, AccountSecurityState state) {
    return ProfileSection(
      title: 'basic_information'.tr(),
      child: Column(
        children: [
          ProfileField(
            icon: Assets.iconProfile, // placeholder icon
            title: "real_name".tr(),
            subtitle: "real_name_description".tr(),
            trailing: AneText(
              sl<UserCubit>().state.userInfo?.realName ?? "unset".tr(),
              style: context.textTheme.title.fs13,
            ),
            onTap: (sl<UserCubit>().state.userInfo?.realName ?? "").isEmpty
                ? () {
                    sl<NavigatorService>().push(
                      AppRouter.updateProfile,
                      arguments: {
                        'initialValue': '',
                        'field': UserFieldType.realName,
                      },
                    );
                  }
                : null,
          ),
          const ProfileFieldDivider(),
          BlocSelector<UserCubit, UserState, String>(
            selector: (state) => state.userInfo?.phoneNo ?? '',
            builder: (context, phoneNo) {
              return ProfileField(
                icon: Assets.iconPhone2,
                title: "phone".tr(),
                subtitle: phoneNo.isEmpty ? "phone_no_tips".tr() : phoneNo.toMaskedFormat(),
                trailing: AneText(
                  phoneNo.isEmpty ? 'unset'.tr() : 'set'.tr(),
                  style: context.textTheme.title.fs13.copyWith(
                    color: phoneNo.isEmpty ? Colors.red[300] : Colors.black38,
                  ),
                ),
                onTap: phoneNo.isEmpty
                    ? () {
                        _navigateToUpdateProfileScreen(context, phoneNo, UserFieldType.phone);
                      }
                    : null,
              );
            },
          ),
          const ProfileFieldDivider(),
          BlocSelector<UserCubit, UserState, String>(
            selector: (state) => state.userInfo?.email ?? '',
            builder: (context, email) {
              return ProfileField(
                icon: Assets.iconMail2,
                title: "email".tr(),
                subtitle: email.isEmpty ? "email_tips".tr() : email,
                trailing: AneText(
                  email.isEmpty ? 'unset'.tr() : 'set'.tr(),
                  style: context.textTheme.title.fs13.copyWith(
                    color: email.isEmpty ? Colors.red[300] : Colors.black38,
                  ),
                ),
                onTap: email.isEmpty
                    ? () {
                        _navigateToUpdateProfileScreen(context, email, UserFieldType.email);
                      }
                    : null,
                isLast: true,
              );
            },
          ),
        ],
      ),
    );
  }

  // Bank Card Management Section
  Widget _buildBankCardManagementSection(BuildContext context, AccountSecurityState state) {
    return ProfileSection(
      title: 'bank_card_management'.tr(),
      child: Column(
        children: [
          ProfileField(
            icon: Assets.iconWallet, // placeholder icon
            title: "wallet_address_management".tr(),
            subtitle: "wallet_address_description".tr(),
            trailing: Container(),
            onTap: () {
              if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
                sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.wallet);
              } else {
                SystemUtil.showSetFundPwdDialog(context);
              }
            },
          ),
          const ProfileFieldDivider(),
          ProfileField(
            icon: Assets.iconCreditCard, // placeholder icon
            title: "bank_card_management".tr(),
            subtitle: "bank_card_description".tr(),
            trailing: Container(),
            onTap: () {
              if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
                sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.bankCard);
              } else {
                SystemUtil.showSetFundPwdDialog(context);
              }
            },
          ),
          if (GlobalConfig.needShowManualUSDTWithdrawWidget()) ...[
            const ProfileFieldDivider(),
            ProfileField(
              icon: Assets.iconUSD, // placeholder icon
              title: "usdt_address".tr(),
              subtitle: "usdt_address_description".tr(),
              trailing: Container(),
              onTap: () {
                if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
                  sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.usdt);
                } else {
                  SystemUtil.showSetFundPwdDialog(context);
                }
              },
              isLast: true,
            ),
          ],
        ],
      ),
    );
  }

  // Password Management Section
  Widget _buildPasswordManagementSection(BuildContext context, AccountSecurityState state) {
    return ProfileSection(
      title: 'password_management'.tr(),
      child: Column(
        children: [
          ProfileField(
            icon: Assets.iconLock, // placeholder icon
            title: "change_login_password".tr(),
            subtitle: "change_login_password_description".tr(),
            trailing: Container(),
            onTap: () =>
                sl<NavigatorService>().push(AppRouter.userModifyPwd, arguments: SetPasswordType.modifyLoginPwd),
          ),
          const ProfileFieldDivider(),
          ProfileField(
            icon: Assets.iconLock, // placeholder icon
            title: "fund_password".tr(),
            subtitle: "fund_password_description".tr(),
            trailing: Container(),
            onTap: () {
              final type = sl<UserCubit>().state.userInfo!.hasFundPwd
                  ? SetPasswordType.modifyFundPwd
                  : SetPasswordType.setFundPwd;
              sl<NavigatorService>().push(AppRouter.userModifyPwd, arguments: type);
            },
            isLast: true,
          ),
        ],
      ),
    );
  }

  // Others Section
  Widget _buildOthersSection(BuildContext context, AccountSecurityState state) {
    return ProfileSection(
      title: 'others'.tr(),
      child: Column(
        children: [
          ProfileField(
            icon: Assets.iconIdentity, // placeholder icon
            title: sl<UserCubit>().state.userInfo?.userNo ?? "username".tr(),
            subtitle: "my_account".tr(),
            trailing: CommonCopyButton(text: sl<UserCubit>().state.userInfo?.userNo ?? ""),
            onTap: null,
          ),
          const ProfileFieldDivider(),
          ProfileField(
            icon: Assets.iconVideo, // placeholder icon
            title: "video".tr(),
            subtitle: "video_interface_display".tr(),
            trailing: BlocBuilder<UserCubit, UserState>(
              builder: (context, state) {
                return CommonSwitch(
                  value: state.userInfo?.tiktokTabVisible ?? true,
                  onChanged: (bool value) => context.read<AccountSecurityCubit>().updateMovieTabVisible(value),
                );
              },
            ),
            onTap: null,
            isLast: true,
          ),
        ],
      ),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<AccountSecurityCubit, AccountSecurityState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 20.gw),
              // Basic Information Section
              _buildBasicInformationSection(context, state),
              SizedBox(height: 20.gw),
              // Bank Card Management Section
              _buildBankCardManagementSection(context, state),
              SizedBox(height: 20.gw),
              // Password Management Section
              _buildPasswordManagementSection(context, state),
              SizedBox(height: 20.gw),
              // Others Section
              _buildOthersSection(context, state),
              SizedBox(height: 20.gw),
            ],
          ),
        );
      },
    );
  }
}
