import 'package:equatable/equatable.dart';
import 'package:flutter/widgets.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/image_captcha_entity.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/features/page/login/login_state.dart';

class RegisterState extends Equatable {
  final AuthMethodType authMethodType;
  final LoginType registerType;
  final CaptchaType captchaType;
  final String username;
  final String phone;
  final String email;
  final String password;
  final String confirmPassword;
  final String? verificationCode;
  final String smsCode;
  final String inviteCode;
  final String channelCode;
  final bool isPasswordVisible;
  final SimplyNetStatus registerStatus;
  final int smsCountdown;
  final bool isSmsTimerActive;
  final int emailCountdown;
  final bool isEmailTimerActive;
  final ImageCaptchaEntity? captchaModel;
  final Country? selectedCountry;
  final TextEditingController usernameController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final TextEditingController verificationCodeController;
  final TextEditingController phoneController;
  final TextEditingController emailController;
  final TextEditingController smsCodeController;

  const RegisterState({
    this.authMethodType = AuthMethodType.both,
    this.registerType = LoginType.userName,
    this.captchaType = CaptchaType.wangYi,
    this.username = '',
    this.email = '',
    this.password = '',
    this.confirmPassword = '',
    this.verificationCode,
    this.smsCode = '',
    this.inviteCode = '',
    this.channelCode = '',
    this.isPasswordVisible = false,
    this.registerStatus = SimplyNetStatus.idle,
    this.smsCountdown = 0,
    this.isSmsTimerActive = false,
    this.emailCountdown = 0,
    this.isEmailTimerActive = false,
    this.captchaModel,
    this.selectedCountry,
    this.phone = '',
    required this.usernameController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.verificationCodeController,
    required this.phoneController,
    required this.emailController,
    required this.smsCodeController,
  });

  RegisterState copyWith({
    AuthMethodType? authMethodType,
    LoginType? registerType,
    CaptchaType? captchaType,
    String? username,
    String? password,
    String? phone,
    String? email,
    String? confirmPassword,
    String? verificationCode,
    String? smsCode,
    String? inviteCode,
    String? channelCode,
    bool? isPasswordVisible,
    SimplyNetStatus? registerStatus,
    int? smsCountdown,
    bool? isSmsTimerActive,
    int? emailCountdown,
    bool? isEmailTimerActive,
    bool? isShowCaptcha,
    ImageCaptchaEntity? captchaModel,
    Country? selectedCountry,
    TextEditingController? usernameController,
    TextEditingController? passwordController,
    TextEditingController? confirmPasswordController,
    TextEditingController? verificationCodeController,
    TextEditingController? emailController,
    TextEditingController? smsCodeController,
    TextEditingController? phoneController,
  }) {
    return RegisterState(
      authMethodType: authMethodType ?? this.authMethodType,
      registerType: registerType ?? this.registerType,
      captchaType: captchaType ?? this.captchaType,
      username: username ?? this.username,
      password: password ?? this.password,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      verificationCode: verificationCode ?? this.verificationCode,
      smsCode: smsCode ?? this.smsCode,
      inviteCode: inviteCode ?? this.inviteCode,
      channelCode: channelCode ?? this.channelCode,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      registerStatus: registerStatus ?? this.registerStatus,
      smsCountdown: smsCountdown ?? this.smsCountdown,
      isSmsTimerActive: isSmsTimerActive ?? this.isSmsTimerActive,
      emailCountdown: emailCountdown ?? this.emailCountdown,
      isEmailTimerActive: isEmailTimerActive ?? this.isEmailTimerActive,
      captchaModel: captchaModel ?? this.captchaModel,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      verificationCodeController: verificationCodeController ?? this.verificationCodeController,
      emailController: emailController ?? this.emailController,
      smsCodeController: smsCodeController ?? this.smsCodeController,
      phoneController: phoneController ?? this.phoneController,
      usernameController: usernameController ?? this.usernameController,
      passwordController: passwordController ?? this.passwordController,
      confirmPasswordController: confirmPasswordController ?? this.confirmPasswordController,
    );
  }

  @override
  List<Object?> get props => [
        authMethodType,
        registerType,
        captchaType,
        username,
        phone,
        email,
        smsCode,
        password,
        confirmPassword,
        verificationCode,
        inviteCode,
        channelCode,
        isPasswordVisible,
        registerStatus,
        smsCountdown,
        isSmsTimerActive,
        emailCountdown,
        isEmailTimerActive,
        captchaModel,
        selectedCountry,
        verificationCodeController,
        phoneController,
        usernameController,
        passwordController,
        confirmPasswordController,
      ];
}
