import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/models/entities/login_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_singleton.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/open_install_manager/open_install_manager.dart';
import 'package:wd/features/page/login/login_state.dart';
import 'package:wd/features/page/login/register/register_state.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../../../../core/constants/enums.dart';
import '../../../../shared/widgets/wangyi_captcha/wangyi_captcha.dart';

class RegisterCubit extends Cubit<RegisterState> {
  RegisterCubit()
      : super(RegisterState(
          verificationCodeController: TextEditingController(),
          phoneController: TextEditingController(),
          emailController: TextEditingController(),
          smsCodeController: TextEditingController(),
          usernameController: TextEditingController(),
          passwordController: TextEditingController(),
          confirmPasswordController: TextEditingController(),
        )) {
    fetchCaptchaType();
    initConfig();
    _initializeCountry();
  }

  /// Initialize default country
  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    emit(state.copyWith(selectedCountry: defaultCountry));
  }

  bool _isSwitching = false; // 添加切换状态标志位
  Timer? _captchaTimer;
  Timer? _smsTimer;
  Timer? _emailTimer;

  void setUsername(String username) {
    emit(state.copyWith(username: username));
  }

  void setPhone(String phone) {
    emit(state.copyWith(phone: phone));
  }

  void setEmail(String email) {
    emit(state.copyWith(email: email));
  }

  void setSmsCode(String smsCode) {
    emit(state.copyWith(smsCode: smsCode));
  }

  void setVerificationCode(String verificationCode) {
    emit(state.copyWith(verificationCode: verificationCode));
  }

  void setPassword(String password) {
    emit(state.copyWith(password: password));
  }

  void setConfirmPassword(String confirmPassword) {
    emit(state.copyWith(confirmPassword: confirmPassword));
  }

  /// Update selected country
  void updateSelectedCountry(Country country) {
    emit(state.copyWith(selectedCountry: country));
  }

  void setInviteCode(String inviteCode) {
    emit(state.copyWith(inviteCode: inviteCode));
  }

  void setChannelCodeCode(String channelCode) {
    emit(state.copyWith(channelCode: channelCode));
  }

  void togglePasswordVisibility() {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  /// 获取验证类型
  fetchCaptchaType() async {
    final registerCaptchaType = await GlobalConfig().getConfigValueByKey("register_captcha");
    if (registerCaptchaType != null) {
      final captchaType = CaptchaType.values.firstWhere(
        (type) {
          return type.index.toString() == registerCaptchaType;
        },
        orElse: () => CaptchaType.wangYi,
      );
      emit(state.copyWith(captchaType: captchaType));
    }
    if (state.captchaType == CaptchaType.picture) {
      fetchImageCaptcha();
    }
  }

  bool isFetchingCaptcha = false;

  int _captchaRefreshCount = 0;
  DateTime? _lastRefreshTime;

  void fetchImageCaptcha() async {
    if (state.captchaType != CaptchaType.picture) return;

    if (_captchaRefreshCount >= 3) {
      final now = DateTime.now();
      if (_lastRefreshTime != null && now.difference(_lastRefreshTime!).inSeconds < 10) {
        GSEasyLoading.showToast("请勿频繁刷新验证码");
        return;
      }
    }

    if (isFetchingCaptcha) return;
    isFetchingCaptcha = true;
    _captchaRefreshCount++;
    _lastRefreshTime = DateTime.now();

    final result = await UserApi.fetchCaptcha();
    if (result != null && !isClosed) {
      emit(state.copyWith(captchaModel: result));
    }
    isFetchingCaptcha = false;
  }

  void _resetCaptchaTimer() {
    _captchaTimer?.cancel();
    _captchaTimer = Timer(const Duration(minutes: 1), () {
      fetchImageCaptcha();
    });
  }

  /// Start SMS verification code countdown timer
  void _startSmsTimer() {
    // Cancel any existing timer
    _smsTimer?.cancel();

    emit(state.copyWith(smsCountdown: 60, isSmsTimerActive: true));

    _smsTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.smsCountdown <= 1) {
        timer.cancel();
        _smsTimer = null;
        if (!isClosed) {
          emit(state.copyWith(smsCountdown: 0, isSmsTimerActive: false));
        }
      } else {
        if (!isClosed) {
          emit(state.copyWith(smsCountdown: state.smsCountdown - 1));
        }
      }
    });
  }

  /// Start Email verification code countdown timer
  void _startEmailTimer() {
    // Cancel any existing timer
    _emailTimer?.cancel();

    emit(state.copyWith(emailCountdown: 60, isEmailTimerActive: true));

    _emailTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.emailCountdown <= 1) {
        timer.cancel();
        _emailTimer = null;
        if (!isClosed) {
          emit(state.copyWith(emailCountdown: 0, isEmailTimerActive: false));
        }
      } else {
        if (!isClosed) {
          emit(state.copyWith(emailCountdown: state.emailCountdown - 1));
        }
      }
    });
  }

  @override
  Future<void> close() {
    _captchaTimer?.cancel();
    _smsTimer?.cancel();
    _emailTimer?.cancel();
    return super.close();
  }

  void _showWangYiCaptcha({
    required String account,
    required Function(String) onSuccess,
  }) {
    WangYiCaptcha().show(
      account: account,
      captchaId: kWangYiVerityKey,
      onSuccess: onSuccess,
      onValidateFailClose: () {
        if (!isClosed) {
          emit(state.copyWith(registerStatus: SimplyNetStatus.idle));
        }
      },
      onError: () {
        GSEasyLoading.showToast('验证失败');
        emit(state.copyWith(registerStatus: SimplyNetStatus.failed));
      },
    );
  }

  Future<void> initConfig() async {
    var systemConfig = GlobalConfig().systemConfig;

    final registerMethod = AuthMethodType.fromConfig(systemConfig.loadLoginAndRegWay.register);
    final initialType = systemConfig.loadLoginAndRegWay.defRegister == '1' ? LoginType.phone : LoginType.userName;

    emit(state.copyWith(
      authMethodType: registerMethod,
      registerType: initialType,
    ));
  }

  void switchRegisterType() {
    // 如果正在切换中，直接返回
    if (_isSwitching) return;
    // 隐藏键盘
    sl<NavigatorService>().unFocus();
    _isSwitching = true; // 标记开始切换
    final newType = state.registerType == LoginType.userName ? LoginType.phone : LoginType.userName;

    state.usernameController.clear();
    state.passwordController.clear();
    state.verificationCodeController.clear();
    state.phoneController.clear();
    state.emailController.clear();
    state.smsCodeController.clear();
    state.confirmPasswordController.clear();

    emit(state.copyWith(
      username: '',
      phone: '',
      email: '',
      password: '',
      smsCode: '',
      verificationCode: null,
      registerStatus: SimplyNetStatus.idle,
      registerType: newType,
    ));
    // 使用 Future.delayed 确保状态更新完成
    Future.delayed(const Duration(milliseconds: 300), () {
      _isSwitching = false; // 标记切换完成
    });
  }

  void switchToEmailRegister() {
    // 如果正在切换中，直接返回
    if (_isSwitching) return;
    // 隐藏键盘
    sl<NavigatorService>().unFocus();
    _isSwitching = true; // 标记开始切换

    state.usernameController.clear();
    state.passwordController.clear();
    state.verificationCodeController.clear();
    state.phoneController.clear();
    state.emailController.clear();
    state.smsCodeController.clear();
    state.confirmPasswordController.clear();

    emit(state.copyWith(
      username: '',
      phone: '',
      email: '',
      password: '',
      smsCode: '',
      verificationCode: null,
      registerStatus: SimplyNetStatus.idle,
      registerType: LoginType.email,
    ));
    // 使用 Future.delayed 确保状态更新完成
    Future.delayed(const Duration(milliseconds: 300), () {
      _isSwitching = false; // 标记切换完成
    });
  }

  void register({LoginType? loginType}) async {
    sl<NavigatorService>().unFocus();

    if (loginType == LoginType.phone && !state.authMethodType.supportsPhone) {
      GSEasyLoading.showToast('不支持手机号注册');
      return;
    }
    if (loginType == LoginType.userName && !state.authMethodType.supportsAccount) {
      GSEasyLoading.showToast('不支持账号注册');
      return;
    }

    if (loginType == LoginType.phone) {
      if (state.phone.isEmpty) {
        GSEasyLoading.showToast('请输入手机号');
        return;
      }
      // Validate phone number length and format
      final bool isValidLength = state.phone.length == 11;
      final bool isValidFormat = RegExp(r'^1[3-9]\d{9}$').hasMatch(state.phone);

      if (!isValidLength || !isValidFormat) {
        GSEasyLoading.showToast('请输入正确的手机号');
        return;
      }
      if (state.smsCode.isEmpty) {
        GSEasyLoading.showToast('请输入短信验证码');
        return;
      }
    } else if (loginType == LoginType.userName) {
      if (state.username.isEmpty) {
        GSEasyLoading.showToast('请输入用户名');
        return;
      }
    }
    if (state.password.isEmpty) {
      GSEasyLoading.showToast('请输入密码');
      return;
    }
    if (state.password != state.confirmPassword) {
      GSEasyLoading.showToast('两次密码输入不一致');
      return;
    }
    if (state.inviteCode.isEmpty) {
      GSEasyLoading.showToast('请输入邀请码');
      return;
    }
    if (state.captchaType == CaptchaType.picture && state.verificationCode == null) {
      GSEasyLoading.showToast('请输入验证码');
      return;
    }

    if (state.captchaType == CaptchaType.wangYi && loginType == LoginType.userName) {
      _showWangYiCaptcha(
        account: state.username,
        onSuccess: (result) {
          fetchRegisterApi(wangYiRes: result, loginType: loginType);
        },
      );
    } else {
      fetchRegisterApi(loginType: loginType);
    }
  }

  void fetchRegisterApi({String? wangYiRes, LoginType? loginType}) async {
    try {
      GSEasyLoading.showLoading();
      final result = await UserApi.doRegisterAndLogin(
        userName: loginType == LoginType.phone ? state.phone : state.username,
        password: state.password,
        invitationCode: state.inviteCode,
        channelCode: state.channelCode,
        verificationCode: state.verificationCode,
        verificationUUID: state.captchaModel?.uuid,
        wangYiCaptcha: wangYiRes,
        isPhoneNo: loginType == LoginType.phone ? true : null,
        smsCode: loginType == LoginType.phone ? state.smsCode : null,
        areaCode: state.selectedCountry?.areaCode,
      );
      GSEasyLoading.dismiss();
      state.verificationCodeController.text = '';
      state.smsCodeController.text = '';
      fetchImageCaptcha();
      if (result.errorStr == null && result.tokenUser != null) {
        autoLogin(result.tokenUser);
        sl<MainScreenCubit>().fetchNoticeDialogListData(); // 获取系统公告弹窗
        emit(state.copyWith(registerStatus: SimplyNetStatus.success));
        await Future.delayed(const Duration(milliseconds: 200));
        sl<NavigatorService>().popUntilOrPush(AppRouter.nav);
        GSEasyLoading.dismiss();
        OpenInstallManager.instance.reportUserRegistration(); // op上报注册成功
      } else {
        GSEasyLoading.showToast(result.errorStr ?? '注册失败');
        emit(state.copyWith(registerStatus: SimplyNetStatus.failed));
      }
    } catch (e) {
      fetchImageCaptcha();
    }
  }

  autoLogin(LoginTokenUser? tokenUser) async {
    if (tokenUser == null) return;
    GSEasyLoading.showLoading(message: '正在自动登录中');
    sl<UserCubit>().setLoginInfo(tokenUser);
    emit(state.copyWith(registerStatus: SimplyNetStatus.success));
    GSEasyLoading.showToast("登录成功");
  }

  void updateSmsCodeController(String smsCode) => state.verificationCodeController.text = smsCode;

  /// Send SMS verification code for registration
  Future<void> sendSmsVerificationCodeForRegister() async {
    if (state.phone.isEmpty) {
      GSEasyLoading.showToast('请输入手机号');
      return;
    }

    // Validate phone number
    final bool isValidLength = state.phone.length == 11;
    final bool isValidFormat = RegExp(r'^1[3-9]\d{9}$').hasMatch(state.phone);

    if (!isValidLength || !isValidFormat) {
      GSEasyLoading.showToast('请输入正确的手机号');
      return;
    }

    if (state.isSmsTimerActive) {
      return; // Timer is already active
    }

    try {
      GSEasyLoading.showLoading();

      final result = await UserApi.doSendSmsCode(
        phoneNo: state.phone,
        areaCode: state.selectedCountry?.areaCode,
      );

      if (result) {
        GSEasyLoading.showToast('短信验证码发送成功');
        _startSmsTimer();

        // Auto-fill SMS code in debug mode
        if (kDebug) {
          _handleTestUseGetSmsCode();
        }
      } else {
        GSEasyLoading.showToast('短信验证码发送失败');
      }
    } catch (e) {
      GSEasyLoading.showToast('短信验证码发送失败');
    } finally {
      GSEasyLoading.dismiss();
    }
  }

  /// Send Email verification code for registration
  Future<void> sendEmailVerificationCodeForRegister() async {
    if (state.email.isEmpty) {
      GSEasyLoading.showToast('请输入邮箱');
      return;
    }

    // Validate email format
    final bool isValidEmail = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(state.email);
    if (!isValidEmail) {
      GSEasyLoading.showToast('请输入正确的邮箱格式');
      return;
    }

    if (state.isEmailTimerActive) {
      return; // Timer is already active
    }

    try {
      GSEasyLoading.showLoading();

      final result = await UserApi.doSendMailCode(
        mail: state.email,
      );

      if (result) {
        GSEasyLoading.showToast('邮箱验证码发送成功');
        _startEmailTimer();
      } else {
        GSEasyLoading.showToast('邮箱验证码发送失败');
      }
    } catch (e) {
      GSEasyLoading.showToast('邮箱验证码发送失败');
    } finally {
      GSEasyLoading.dismiss();
    }
  }

  /// Get test SMS code in debug mode
  Future<void> _handleTestUseGetSmsCode() async {
    try {
      final result = await UserApi.testUseGetSmsCode(state.phone);
      if (result.isNotEmpty) {
        state.smsCodeController.text = result;
        emit(state.copyWith(smsCode: result));
      }
    } catch (e) {
      // Ignore errors in test mode
    }
  }
}
