import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_avif/flutter_avif.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/utils/system_util.dart';

class AppImage extends StatelessWidget {
  final String imageUrl;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BoxFit? fit;
  final double? width;
  final double? height;
  final Rect? centerSlice;
  double? radius; //圆角
  Color? borderColor; //边框颜色
  final double? placeHolderHeight;
  final BaseCacheManager? cacheManager;

  AppImage({
    super.key,
    required this.imageUrl,
    this.placeholder,
    this.errorWidget,
    this.fit,
    this.width,
    this.height,
    this.centerSlice,
    this.radius,
    this.borderColor,
    this.placeHolderHeight,
    this.cacheManager,
  });

  @override
  Widget build(BuildContext context) {
    final isLocalImage = imageUrl.startsWith('assets/');
    final isSvgImage = imageUrl.endsWith('.svg');

    if  (isLocalImage) {
      if (isSvgImage) {
        return SvgPicture.asset(
          imageUrl,
          fit: fit ?? BoxFit.cover,
          width: width,
          height: height,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(context),
        );
      }
       return Image.asset(
         imageUrl,
         fit: fit ?? BoxFit.cover,
         width: width,
         height: height,
         centerSlice: centerSlice,
         errorBuilder: (context, error, stackTrace) => _buildErrorWidget(context),
       );
     }


    /// TODO avif
    // if (SystemUtil.isAvifSupport && imageUrl.endsWith('.png') && kChannel == "YL" && !kIsFranchisee) {
    //   final avifUrl = imageUrl.replaceAll(".png", ".avif").replaceAll(".com/", ".com/yl/avif/");
    //
    //   return AvifImage.network(
    //     avifUrl,
    //     width: width,
    //     height: height,
    //     fit: fit ?? BoxFit.cover,
    //     errorBuilder: (context, _, __) {
    //       return _buildNetworkImage(context);
    //     },
    //   );
    // }

    return _buildNetworkImage(context);
  }

  Widget _buildNetworkImage(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      imageBuilder: (context, imageProvider) => _buildImageContainer(Image(image: imageProvider, fit: fit)),
      placeholder: (context, url) => _buildPlaceholder(context),
      errorWidget: (context, url, error) => _buildErrorWidget(context),
      fit: fit ?? BoxFit.cover,
      width: width,
      height: height,
      fadeInDuration: const Duration(milliseconds: 1),
      fadeOutDuration: const Duration(milliseconds: 1),
      cacheManager: cacheManager,
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return _buildImageContainer(placeholder ?? Container(color: Theme.of(context).dividerColor));
  }

  Widget _buildErrorWidget(BuildContext context) {
    return errorWidget ?? _buildPlaceholder(context);
  }

  Widget _buildImageContainer(Widget image) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius ?? 0),
      ),
      clipBehavior: Clip.hardEdge,
      child: image,
    );
  }
}
