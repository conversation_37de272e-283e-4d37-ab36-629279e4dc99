import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class PhoneInputField extends StatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final String? errorText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<Country>? onCountryChanged;
  final Country? selectedCountry;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;

  const PhoneInputField({
    super.key,
    required this.controller,
    this.hintText,
    this.errorText,
    this.onChanged,
    this.onCountryChanged,
    this.selectedCountry,
    this.enabled = true,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
  });

  @override
  State<PhoneInputField> createState() => _PhoneInputFieldState();
}

class _PhoneInputFieldState extends State<PhoneInputField> {
  Country? _selectedCountry;
  final GlobalKey _prefixKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    if (widget.selectedCountry != null) {
      _selectedCountry = widget.selectedCountry;
    } else {
      _selectedCountry = await CountryService.instance.getDefaultCountry();
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Phone number input field with clickable country prefix
        _buildPhoneInputField(),

        // Error text
        if (widget.errorText != null) ...[
          SizedBox(height: 4.gw),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.gw),
            child: Text(
              widget.errorText!,
              style: TextStyle(
                fontSize: 12.gw,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Shows country picker dropdown
  void _showCountryPicker() async {
    final countries = await CountryService.instance.getCountries();
    if (!mounted || countries.isEmpty) return;

    // Find the RenderBox of the prefix icon to position dropdown
    final RenderBox? renderBox = _prefixKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    showMenu<Country>(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx, // Left position aligned with prefix
        position.dy + size.height + 4.gw, // Just below the prefix icon
        position.dx + 300.gw, // Right position (dropdown width)
        position.dy + size.height + 300.gw, // Max height for dropdown
      ),
      items: countries.map((Country country) {
        final isSelected = _selectedCountry?.code == country.code;
        return PopupMenuItem<Country>(
          value: country,
          child: Container(
            width: 150.gw,
            padding: EdgeInsets.symmetric(vertical: 8.gw, horizontal: 12.gw),
            decoration: BoxDecoration(
              color: isSelected ? context.theme.primaryColor.withOpacity(0.1) : null,
              borderRadius: BorderRadius.circular(8.gw),
            ),
            child: Row(
              children: [
                Text(
                  country.flag,
                  style: TextStyle(fontSize: 20.gw),
                ),
                SizedBox(width: 12.gw),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        country.name,
                        style: context.textTheme.primary.copyWith(
                          fontSize: 14.gw,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected ? context.theme.primaryColor : null,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        '+${country.areaCode}',
                        style: context.textTheme.secondary.copyWith(
                          fontSize: 12.gw,
                          color: isSelected ? context.theme.primaryColor : null,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check,
                    color: context.theme.primaryColor,
                    size: 16.gw,
                  ),
              ],
            ),
          ),
        );
      }).toList(),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.gw),
      ),
    ).then((Country? selectedCountry) {
      if (selectedCountry != null) {
        setState(() {
          _selectedCountry = selectedCountry;
        });
        widget.onCountryChanged?.call(selectedCountry);
      }
    });
  }

  /// Builds phone input field with IconTextfield-style design
  Widget _buildPhoneInputField() {
    return SizedBox(
      width: double.infinity,
      height: 60.gw,
      child: TextField(
        controller: widget.controller,
        enabled: widget.enabled,
        onChanged: widget.onChanged,
        onTap: widget.onTap,
        keyboardType: TextInputType.phone,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(15),
        ],
        decoration: InputDecoration(
          hintText: widget.hintText ?? 'phone_number'.tr(),
          hintStyle: context.textTheme.highlight,
          contentPadding: EdgeInsets.only(
            right: 16.gw,
            top: 16.gh,
            bottom: 16.gh,
          ),
          prefixIconConstraints: BoxConstraints(
            minWidth: 76.gw,
            maxWidth: 76.gw,
          ),
          suffixIcon: widget.suffixIcon,
          prefixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: widget.enabled ? _showCountryPicker : null,
                child: Container(
                  key: _prefixKey,
                  width: 56.gw,
                  height: 60.gw,
                  decoration: BoxDecoration(
                    color: context.colorTheme.iconBgA,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.gw),
                      bottomLeft: Radius.circular(12.gw),
                    ),
                  ),
                  child: Center(
                    child: _selectedCountry != null
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _selectedCountry!.flag,
                                style: TextStyle(fontSize: 14.gw),
                              ),
                              SizedBox(height: 1.gw),
                              Text(
                                '+${_selectedCountry!.areaCode}',
                                style: context.textTheme.regular.copyWith(
                                  fontSize: 10.gw,
                                  color: context.colorTheme.textPrimary,
                                ),
                              ),
                              Icon(
                                Icons.keyboard_arrow_down,
                                size: 12.gw,
                                color: context.colorTheme.textSecondary,
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.phone,
                                size: 16.gw,
                                color: context.colorTheme.textSecondary,
                              ),
                              Icon(
                                Icons.keyboard_arrow_down,
                                size: 12.gw,
                                color: context.colorTheme.textSecondary,
                              ),
                            ],
                          ),
                  ),
                ),
              ),
              SizedBox(width: 20.gw),
            ],
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.theme.primaryColor,
              width: 1,
            ),
          ),
        ),
      ),
    );
  }
}
