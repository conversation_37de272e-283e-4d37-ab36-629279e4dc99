import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/balance/currency_symbol_icon.dart';
import 'package:wd/shared/widgets/common_textfield.dart';

class TopUpTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final double maxLimit;
  final double? rates;
  final Function(String) onChanged;
  final bool enabled;
  final TextStyle? style;

  const TopUpTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.maxLimit,
    this.rates,
    required this.onChanged,
    this.enabled = true,
    this.style,
  });

  @override
  Widget build(BuildContext context) {

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 40.gw,
          child: CommonTextField(
            controller: controller,
            hintText: hintText,
            fillColor: context.colorTheme.foregroundColor,
            radius: 8.gw,

            // style: style ?? TextStyle(color: const Color(0xffCDB296), fontSize: 20.fs, fontWeight: FontWeight.w600),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            // 使内容垂直居中
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')), // 只允许一个小数点
            ],
            prefixIconPadding: EdgeInsets.only(left: 12.gw, right: 8.gw),
            prefixIcon: CurrencySymbolIcon(style: CurrencySymbolIconStyle.secondary),
            onChanged: (value) {
              if (value.isEmpty) {
                onChanged(value);
                return;
              }

              double inputAmount = double.tryParse(value) ?? 0;
              if (inputAmount > maxLimit) {
                inputAmount = maxLimit;
                controller.text = inputAmount.removeZeros;
              }
              onChanged(controller.text);
            },
          ),
        ),
        if (rates != null && controller.text.isNotEmpty && kChannel != "JS") ...[
          SizedBox(height: 8.gw),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              rates == null
                  ? Container()
                  : SizedBox(
                      height: 20.gw,
                      child: Row(
                        children: [
                          Text(
                            "当前汇率换算 ",
                            style: TextStyle(
                              color: const Color(0xffC2AA8E),
                              fontSize: 14.fs,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Text(
                            "≈ ${(double.parse(controller.text) / rates!).toStringAsFixed(2)} USDT",
                            style: TextStyle(
                              color: const Color(0xffC2AA8E),
                              fontSize: 16.fs,
                              fontFamily: 'DINCond-Bold',
                              height: 1.5.gw,
                              fontWeight: FontWeight.w500,
                            ),
                          )
                        ],
                      ),
                  ),
            ],
          ),
        ],
      ],
    );
  }

}
