import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class SectionContainer extends StatelessWidget {
  final String title;
  final String imagePath;
  final Widget? suffixIcon;
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? borderRadius;
  final Color? backgroundColor;
  final bool showDivider;
  final bool showTitleBar;
  final Function()? onTap;

  const SectionContainer({
    super.key,
    required this.title,
    required this.imagePath,
    this.suffixIcon,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.showDivider = true,
    this.showTitleBar = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        color: backgroundColor ??  context.theme.cardColor,
        borderRadius: BorderRadius.circular(borderRadius ?? 16.gw),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTitleBar) ...[
            GestureDetector(
              onTap: onTap,
              child: Container(
                color: context.theme.cardColor,
                height: 52.gw,
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Image.asset(imagePath, height: 20.gw),
                        SizedBox(width: 8.gw),
                        AneText(
                          title,
                          style: context.textTheme.secondary.fs20.w500,
                        ),
                      ],
                    ),
                    if (suffixIcon != null) suffixIcon!,
                  ],
                ),
              ),
            )
          ],
          if (showDivider)
            Divider(
              color: context.colorTheme.foregroundColor,
              thickness: 1,
              height: 1,
            ),
          child,
        ],
      ),
    );
  }
}
