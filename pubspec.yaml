name: wd
description: "WD"

publish_to: "none"

version: 1.0.0+1


environment:
  sdk: ">=3.4.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # 📦 核心
  flutter_bloc:                   ^8.1.6      # Bloc 状态管理框架
  get_it:                         ^8.0.2      # 依赖注入容器
  equatable:                      ^2.0.5      # 值比较工具
  flutter_screenutil:             ^5.9.3      # 屏幕适配
  easy_localization:              ^3.0.7+1    # 多语言国际化
  cached_network_image:           ^3.3.0      # 图片缓存组件
  fluro:                          ^2.0.5      # 路由框架
  rxdart:                         ^0.27.7     # 响应式编程库
  collection:                     ^1.18.0     # 集合工具扩展（增强 List/Map 操作）
  shared_preferences:             ^2.2.3      # 本地持久化存储
  path:                           ^1.9.0      # 路径操作工具
  basic_utils:                    ^5.7.0      # 常用工具方法集合
  logger:                         ^2.4.0      # 日志工具

  # 🌐 网络编程
  dio:                            ^5.5.0+1    # 网络请求库
  dio_smart_retry:                ^6.0.0      # Dio 网络请求自动重试插件
  http:                           ^1.3.0      # Dart 原生 HTTP 客户端
  connectivity_plus:              ^6.1.3      # 网络连接状态监听
  ntp:                            ^2.0.0      # 网络时间同步

  # 🔐 安全
  encrypt:                        ^5.0.3      # 加解密
  permission_handler:             ^11.3.1     # 权限申请
  captcha_plugin_flutter:         ^1.1.3      # 网易验证码插件
  uuid:                           ^4.5.1      # UUID 生成
  device_info_plus:               ^10.1.2     # 设备信息获取
  memory_info:                    ^0.0.4      # 内存信息
  package_info_plus:              ^8.0.0      # 应用包信息（版本号、包名等）

  # 🔔 推送
  flutter_plugin_engagelab:       1.2.4       # EngageLab 推送服务
  openinstall_flutter_plugin:     ^2.5.2      # OpenInstall 应用分发与激活统计
  app_badge_plus:                 ^1.2.0      # App 图标角标设置（iOS / Android）

  # 🧩 UI组件
  visibility_detector:            ^0.4.0+2    # 判断组件是否可见
  flutter_native_splash:          2.4.4       # 原生启动图
  fluttertoast:                   ^8.2.5      # Toast 弹窗
  carousel_slider:                ^5.0.0      # 轮播图组件
  pull_to_refresh:                ^2.0.0      # 下拉刷新
  flutter_svg:                    ^2.0.10+1   # SVG 图片支持
  draggable_float_widget:         ^0.1.0      # 可拖拽按钮
  anydrawer:                      ^1.0.6      # 自定义抽屉（Drawer）控件
  flutter_inner_shadow:           ^0.0.1      # 内阴影效果
  sticky_and_expandable_list:     ^1.1.3      # 悬浮表头，用于聊天联系人页
  scrollable_positioned_list:     ^0.2.3      # 支持定位的 ListView
  r_dotted_line_border:           ^0.1.1      # 虚线边框
  dotted_decoration:              ^2.0.0      # 虚线装饰器
  qr_flutter:                     ^4.1.0      # 二维码生成
  ## 文字
  auto_size_text:                 ^3.0.0      # 自动缩放文字
  marquee:                        ^2.2.3      # 跑马灯滚动文字
  marquee_text:                   ^2.5.0+1    # 跑马灯文字增强版
  text_scroll:                    ^0.2.0      # 文字滚动（如充值渠道展示）
  ## loading
  flutter_easyloading:            ^3.0.5      # 全局 loading 组件
  loading_animation_widget:       ^1.3.0      # 自定义 Loading 动画
  flutter_spinkit:                ^5.2.1      # Loading 动画集合
  ## 动画
  flutter_staggered_animations:   ^1.1.1      # 分段动画效果（用于列表、网格）
  flutter_animate:                ^4.5.2      # 丰富的动画封装（进出场、颜色、缩放等）
  animated_flip_counter:          ^0.3.4      # 翻转数字动画（计数器）
  like_button:                    ^2.0.5      # 点赞动画按钮
  ## 键盘
  keyboard_dismisser:             ^3.0.0      # 点击空白区域自动收起键盘
  keyboard_avoider:               ^0.2.0      # 自动避开键盘的控件
  flutter_keyboard_visibility:    ^6.0.0      # 解决键盘遮挡

  # 🎥 视频相关
  video_player:                   ^2.9.2      # 视频播放组件
  video_player_android:           ^2.4.12     # 安卓平台支持
  video_player_web_hls:           ^1.3.0      # Web HLS 支持
  media_kit:                      ^1.1.11     # 高性能媒体播放
  media_kit_video:                ^1.2.5      # 媒体播放子库（视频）
  media_kit_libs_video:           ^1.0.5      # 视频播放依赖（Native 库）
  flutter_aliplayer:              ^6.19.1     # 阿里云视频播放器（iOS / Android）

  # 📸 相册/文件
  image:                          ^4.2.0      # 图像处理工具（加载、裁剪、缩放等）
  image_picker:                   ^1.1.2      # 相册 / 拍照选择器（系统原生）
  file_picker:                    ^8.1.4      # 文件选择器
  open_file:                      ^3.5.10     # 打开本地文件（调用系统默认应用）
  flutter_cache_manager:          ^3.3.1      # 图片/文件缓存管理（配合图片库）
  path_provider:                  ^2.1.4      # 文件路径管理
  shelf_static:                   ^1.1.3      # 本地静态文件服务（如内置静态资源）
  wechat_assets_picker:           ^9.4.2      # 图片视频选择器（微信风格）
  wechat_camera_picker:           ^4.3.6      # 相机拍摄（与 assets_picker 搭配使用）
  flutter_avif:                   ^3.0.0      # 支持 AVIF 图片格式
  image_gallery_saver:                        # 保存图片到相册
    git:
      url: https://github.com/hui-z/image_gallery_saver
      ref: master

  # 🌍 WebView
  webview_flutter:                ^4.8.0      # WebView 插件
  webview_flutter_android:        ^3.16.7     # 安卓支持，解决 input='file'
  webview_flutter_wkwebview:      3.17.0      # iOS WKWebView 支持 inline 视频播放
  pointer_interceptor:            ^0.10.1+2   # WebView 手势穿透处理
  flutter_widget_from_html:       ^0.10.6     # 渲染 HTML 内容
  flutter_html:                   ^3.0.0-beta.2 # 渲染 HTML 内容（另一实现）

  # 🧪 其他
  lunar:                          ^1.7.0      # 农历 / 节气计算
  url_launcher:                   ^6.3.1      # 打开外部链接
  timezone:                       ^0.9.4      # 时区支持

  # 💬 即时通讯（可选：如需集成腾讯云 IM）
  tencent_cloud_chat_uikit:                     # 腾讯云 IM UI 组件（本地路径依赖）
    path: packages/tencent/tencent_cloud_chat_uikit-3.1.0+1
  tim_ui_kit_sticker_plugin:                   # 腾讯云 IM 表情插件（本地路径依赖）
    path: packages/tencent/tim_ui_kit_sticker_plugin-3.2.0
  tencent_chat_i18n_tool:         ^2.3.8       # 腾讯云 IM 多语言工具
  tencent_cloud_chat_intl:        ^2.1.0       # 腾讯云 IM 国际化 SDK

  # ☁️ Firebase
  firebase_core:                  ^3.12.1     # Firebase 核心库
  firebase_crashlytics:           ^4.3.4      # 崩溃日志收集
  firebase_auth:                  ^5.6.0      # Firebase 身份验证（含 Google 登录）
  google_sign_in:                 ^6.2.2      # Google 登录支持

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0
  build_runner: ^2.4.11
  json_serializable: ^6.8.0
  flutter_launcher_icons: ^0.14.4
  flutter_flavorizr: ^2.2.3

dependency_overrides:
  flutter_web_frame:
    path: packages/flutter_web_frame
  chewie:
    path: packages/chewie
  fading_edge_scrollview: ^4.1.1
  image_gallery_saver:
    git:
      url: https://github.com/knottx/image_gallery_saver.git
      ref: knottx-latest
  file: ^7.0.0

flutter_flavorizr:
  path: flavorizr.yaml

flutter:
  uses-material-design: true
  assets:
    - assets/translations/
    - assets/json/
    - assets/images/
    - assets/images/ali_animal_game/
    - assets/images/alert/
    - assets/images/activity/
    - assets/images/activity/task/
    - assets/images/common/
    - assets/images/login/
    - assets/images/toolBar/
    - assets/images/mine/
    - assets/images/check_in/
    - assets/images/mine/v3/
    - assets/images/mine/wallet/
    - assets/images/mine/vip/
    - assets/images/mine/tutorial/
    - assets/images/mine/course_tutorial/
    - assets/images/mine/video_coupon/
    - assets/images/mine/recruitment/
    - assets/images/mine/profile/
    - assets/images/avatars/
    - assets/images/channel/
    - assets/images/order/
    - assets/images/settings/
    - assets/images/drawer/
    - assets/images/navigation_bar/
    - assets/images/home/
    - assets/images/home/<USER>/
    - assets/images/home/<USER>/
    - assets/images/home/<USER>/
    - assets/images/dialog/
    - assets/images/transact/
    - assets/images/transact/v3/
    - assets/images/logo/
    - assets/images/promotion/
    - assets/images/chat/
    - assets/images/chat/banner/
    - assets/images/chat/ui/
    - assets/images/welcome/
    - assets/images/tiktok/
    - assets/images/share/
    - assets/images/button/
    - assets/images/video/
    - assets/lottie/
    - assets/html/
    - assets/html/tutorials/withdrawal/
    - assets/html/tutorials/recharge/
    - assets/html/tutorials/transfer/
    - assets/html/tutorials/course/
    - assets/html/captcha/
    - assets/html/about/
    - assets/html/video/
    - assets/video/
    - assets/images/gif/
    - assets/images/mine/promotion_rewards/
    - shorebird.yaml
  fonts:
    - family: AnekDevanagari
      fonts:
        - asset: assets/fonts/AnekDevanagari.ttf